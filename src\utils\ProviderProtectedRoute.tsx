import { Navigate, Outlet, useLocation } from "react-router-dom";
import { useAuth } from "../utils/AuthContext";

const ProviderProtectedRoute = () => {
  const { isAuthenticated, accessToken, role, isProviderProfile } = useAuth();
  const location = useLocation();

  // Check if user is authenticated
  if (!isAuthenticated || !accessToken) {
    return <Navigate to="/login" replace />;
  }

  // Check if user is a provider
  if (role !== "provider") {
    return <Navigate to="/login" replace />;
  }

  // If provider doesn't have a profile yet, redirect to profile page
  // Exception: allow access to profile page itself
  if (!isProviderProfile && location.pathname !== "/profile") {
    return <Navigate to="/profile" replace />;
  }

  // No additional restrictions for approved providers
  // They can access all pages including Profile and Help

  return <Outlet />;
};

export default ProviderProtectedRoute;
