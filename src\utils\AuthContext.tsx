import { createContext, useContext, useEffect, useState, ReactNode } from "react";
import { refresh } from "../apis/auth";
import { catchAsync } from "./helper";
import { logout as logoutApi } from "../apis/auth"; // import your logout API function

interface AuthContextType {
  isAuthenticated: boolean;
  accessToken: string | null;
  role: string | null;
  login: (accessToken: string, refreshToken: string, accessExpires: string, refreshExpires: string, role: string, isProviderProfile?: boolean, isActiveProvider?: boolean) => void;
  logout: () => void;
  refreshToken: () => Promise<boolean>;
  isProviderProfile?: boolean;
  isActiveProvider?: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [isProviderProfile, setIsProviderProfile] = useState<boolean>(false);
  const [isActiveProvider, setIsActiveProvider] = useState<boolean>(false);
  const [accessToken, setAccessToken] = useState<string | null>(null);
  const [role, setRole] = useState<string | null>(null);

  useEffect(() => {
    checkAuth();
  });

  const checkAuth = async () => {
    const storedToken = localStorage.getItem("accessToken");
    const storedRole = localStorage.getItem("role");
    const tokenExpiry = localStorage.getItem("accessExpires");
    const isProviderProfile = localStorage.getItem("isProviderProfile");
    const isActiveProvider = localStorage.getItem("isActiveProvider");

    if (storedToken && tokenExpiry) {
      const isTokenExpired = new Date(tokenExpiry) < new Date();

      if (!isTokenExpired) {
        setAuthState(storedToken, storedRole, isProviderProfile, isActiveProvider);
      } else {
        const refreshed = await refreshToken();
        if (!refreshed) logout();
      }
    } else {
      logout();
    }
  };

  function stringToBool(value: boolean | string | null): boolean {
    if (typeof value === 'string') {
        return value.toLowerCase() === 'true';
    } else if (value === null) {
        return false; // Explicitly check for null
    } else if (typeof value === 'boolean') {
        return value; // Return the boolean value as is
    }
    return false; // Default case
}
  const setAuthState = (token: string, role: string | null, isProviderProfile: string|null|boolean = false, isActiveProvider: string|null|boolean=false) => {
    setIsAuthenticated(true);
    setAccessToken(token);
    setRole(role);
    setIsProviderProfile(stringToBool(isProviderProfile));
    setIsActiveProvider(stringToBool(isActiveProvider));
  };

  const login = (accessToken: string, refreshToken: string, accessExpires: string, refreshExpires: string, role: string, isProviderProfile:boolean = false, isActiveProvider: boolean = false) => {
    // If this is a new provider login (no profile), clear any existing cached profile data
    if (role === "provider" && !isProviderProfile) {
      localStorage.removeItem("providerProfileData");
    }

    localStorage.setItem("accessToken", accessToken);
    localStorage.setItem("refreshToken", refreshToken);
    localStorage.setItem("accessExpires", accessExpires);
    localStorage.setItem("refreshExpires", refreshExpires);
    localStorage.setItem("role", role);
    localStorage.setItem("isProviderProfile", String(isProviderProfile));
    localStorage.setItem("isActiveProvider", String(isActiveProvider));
    setIsProviderProfile(isProviderProfile);
    setIsActiveProvider(isActiveProvider);
    setAuthState(accessToken, role);
  };

  const logout = async () => {
    const refreshToken = localStorage.getItem("refreshToken");

    if (refreshToken) {
      await catchAsync(
        async () => {
          await logoutApi(refreshToken); // API call to logout
        }
      );  
    }

    localStorage.removeItem("accessToken");
    localStorage.removeItem("refreshToken");
    localStorage.removeItem("accessExpires");
    localStorage.removeItem("refreshExpires");
    localStorage.removeItem("role");
    localStorage.removeItem("isProviderProfile");
    localStorage.removeItem("isActiveProvider");
    localStorage.removeItem("providerProfileData"); // Clear cached profile data

    setAccessToken(null);
    setRole(null);
    setIsAuthenticated(false);
    setIsProviderProfile(false);
    setIsActiveProvider(false);
  };

  const refreshToken = async (): Promise<boolean> => {
    const refreshToken = localStorage.getItem("refreshToken");
    if (!refreshToken) return false;

    return await catchAsync(async () => {
      const response = await refresh(refreshToken);

      if (response.status === true) {
        const { access, refresh } = response.data;
        localStorage.setItem("accessToken", access.token);
        localStorage.setItem("accessExpires", access.expires);
        localStorage.setItem("refreshToken", refresh.token);
        localStorage.setItem("refreshExpires", refresh.expires);
        setAuthState(access.token, localStorage.getItem("role"));
        return true;
      }

      return false;
    }, { showToast: false }) ?? false;
    
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, accessToken, role, login, logout, refreshToken, isProviderProfile, isActiveProvider }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
