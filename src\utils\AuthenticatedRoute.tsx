import { Navigate, Outlet } from "react-router-dom";
import { useAuth } from "../utils/AuthContext";

const AuthenticatedRoute = () => {
  const { isAuthenticated, role, isProviderProfile } = useAuth();

  if (isAuthenticated) {
    if (role === "admin") {
      return <Navigate to="/admin" replace />;
    } else if (role === "provider") {
      // New providers without profile go to profile page, existing ones to dashboard
      return <Navigate to={isProviderProfile ? "/dashboard" : "/profile"} replace />;
    }
    return <Navigate to="/dashboard" replace />;
  }

  return <Outlet />;
};

export default AuthenticatedRoute;
