import  { useEffect, useState } from "react";
import { Package, ShoppingCart, Star, AlertCircle, CheckCircle, Clock } from "lucide-react";
import { ProviderDashboardCountResponse } from "../../types/global";
import {  getProviderDeshboardCount } from "../../apis/admin";
import { getProfile } from "../../apis/provider";

const Dashboard = () => {
        const [dashboardData, setDashboardData] = useState<ProviderDashboardCountResponse["data"] | null>(null);
        const [profileStatus, setProfileStatus] = useState<'pending' | 'approved' | 'rejected' | null>(null);
        const [loading, setLoading] = useState(true);

    // Function to fetch profile status instantly
    const fetchProfileStatus = async () => {
        try {
            // Check cached profile first for instant load
            const cachedProfile = localStorage.getItem('providerProfile');
            if (cachedProfile) {
                const profile = JSON.parse(cachedProfile);
                setProfileStatus(profile.isApproved || 'pending');
            }

            // Then fetch fresh data
            const profileRes = await getProfile();
            if (profileRes.status && profileRes.data) {
                const status = profileRes.data.isApproved || 'pending';
                setProfileStatus(status);
                // Update cache
                localStorage.setItem('providerProfile', JSON.stringify(profileRes.data));
            } else {
                setProfileStatus('pending');
            }
        } catch (profileError) {
            setProfileStatus('pending');
        }
    };

    // Function to fetch dashboard data
    const fetchDashboardData = async () => {
        try {
            const dashboardRes = await getProviderDeshboardCount();
            if (dashboardRes.status) {
                setDashboardData(dashboardRes.data);
            }
        } catch (error) {
            console.error("Failed to fetch dashboard data", error);
        }
    };

    useEffect(() => {
        const loadData = async () => {
            setLoading(true);
            // Load profile status first (instant from cache)
            await fetchProfileStatus();
            // Then load dashboard data
            await fetchDashboardData();
            setLoading(false);
        };

        loadData();

        // Set up interval to refresh data every 30 seconds for real-time updates
        const interval = setInterval(() => {
            fetchProfileStatus();
            fetchDashboardData();
        }, 30000);

        return () => clearInterval(interval);
    }, []);
    const stats = [
        {
            title: "Total Products",
            value: dashboardData?.totalProducts,
            change: "+12%",
            changeType: "positive",
            icon: Package,
            color: "blue",
            bgColor: "bg-blue-500/10",
            iconColor: "text-blue-400",
            description: "Active products in inventory"
        },
        {
            title: "Total Orders",
            value: dashboardData?.totalOrders,
            change: "+8%",
            changeType: "positive",
            icon: ShoppingCart,
            color: "green",
            bgColor: "bg-green-500/10",
            iconColor: "text-green-400",
            description: "Orders this month"
        },
        {
            title: "Customer Reviews",
            value: dashboardData?.totalReviews,
            change: "+15%",
            changeType: "positive",
            icon: Star,
            color: "yellow",
            bgColor: "bg-yellow-500/10",
            iconColor: "text-yellow-400",
            description: "Total customer feedback"
        }
    ];

    return (
        <div className="min-h-screen  from-gray-900 to-gray-900">
            {/* Header Section */}
            <div className=" border-b border-gray-700/50">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                        <div>
                            <h1 className="text-2xl sm:text-3xl font-bold text-white">
                                Dashboard Overview
                            </h1>

                        </div>
                    </div>
                </div>
            </div>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-11 sm:px-6 lg:px-8 py-8">
                {/* Profile Status Message */}
                {profileStatus && (
                    <div className="mb-8">
                        {profileStatus === 'pending' && (
                            <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-2xl p-6 flex items-center gap-4">
                                <div className="bg-yellow-500/20 p-3 rounded-full flex justify-center items-center">
                                    <Clock className="w-8 h-8 text-yellow-400" />
                                </div>
                                <div>
                                    <h3 className="text-xl font-semibold text-yellow-300 mb-2">
                                        Profile Under Review
                                    </h3>
                                    <p className="text-yellow-200">
                                        Your profile is pending approval. Our admin team is reviewing your application.
                                        You'll receive an email notification once approved.
                                    </p>
                                </div>
                            </div>
                        )}

                        {profileStatus === 'rejected' && (
                            <div className="bg-red-500/10 border border-red-500/20 rounded-2xl p-6 flex items-center gap-4">
                                <div className="bg-red-500/20 p-3 rounded-full flex justify-center items-center">
                                    <AlertCircle className="w-8 h-8 text-red-400" />
                                </div>
                                <div>
                                    <h3 className="text-xl font-semibold text-red-300 mb-2">
                                        Profile Rejected
                                    </h3>
                                    <p className="text-red-200">
                                        Your profile has been rejected. Please check your email for details and
                                        update your profile accordingly. Contact admin if you need assistance.
                                    </p>
                                </div>
                            </div>
                        )}

                        {profileStatus === 'approved' && (
                            <div className="bg-green-500/10 border border-green-500/20 rounded-2xl p-6 flex items-center gap-4">
                                <div className="bg-green-500/20 p-3 rounded-full flex justify-center items-center">
                                    <CheckCircle className="w-8 h-8 text-green-400" />
                                </div>
                                <div>
                                    <h3 className="text-xl font-semibold text-green-300 mb-2">
                                        Profile Approved
                                    </h3>
                                    <p className="text-green-200">
                                        Congratulations! Your profile has been approved. You can now access all provider features.
                                    </p>
                                </div>
                            </div>
                        )}
                    </div>
                )}

                {/* Stats Grid - Only show when approved */}
                {profileStatus === 'approved' && (
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
                        {loading ? (
                            // Loading skeleton
                            Array.from({ length: 3 }).map((_, index) => (
                                <div
                                    key={index}
                                    className="bg-gray-800/80 backdrop-blur-sm border border-gray-600/60 rounded-2xl p-5 animate-pulse"
                                >
                                    <div className="flex items-start justify-between mb-4">
                                        <div className="bg-gray-700 w-14 h-14 rounded-xl"></div>
                                    </div>
                                    <div className="space-y-2">
                                        <div className="bg-gray-700 h-10 w-20 rounded"></div>
                                        <div className="bg-gray-700 h-6 w-32 rounded"></div>
                                        <div className="bg-gray-700 h-4 w-40 rounded"></div>
                                    </div>
                                </div>
                            ))
                        ) : (
                            // Actual stats
                            stats.map((stat, index) => {
                                const Icon = stat.icon;
                                return (
                                    <div
                                        key={index}
                                        className="group relative bg-gray-800/80 backdrop-blur-sm border border-gray-600/60 rounded-2xl p-5 hover:bg-gray-700/90 hover:border-gray-500/80 transition-all duration-300 hover:scale-[1.02] hover:shadow-xl hover:shadow-black/30"
                                    >
                                        {/* Background Glow Effect */}
                                        <div className={`absolute inset-0 ${stat.bgColor} rounded-2xl opacity-0 group-hover:opacity-20 transition-opacity duration-300`}></div>

                                        {/* Content */}
                                        <div className="relative">
                                            <div className="flex items-start justify-between mb-4">
                                                <div className={`${stat.bgColor} ${stat.iconColor} p-2 rounded-xl group-hover:scale-110 transition-transform duration-300`}>
                                                    <Icon className="w-10 h-10" />
                                                </div>
                                            </div>

                                            <div className="space-y-2">
                                                <p className="text-3xl sm:text-4xl font-bold text-white group-hover:text-white transition-colors">
                                                    {stat.value||0}
                                                </p>
                                                <p className="text-base font-semibold text-gray-200">
                                                    {stat.title||"--"}
                                                </p>
                                                <p className="text-sm text-gray-400">
                                                    {stat.description||"--"}
                                                </p>
                                            </div>
                                        </div>

                                        {/* Hover indicator */}
                                        <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-transparent via-blue-500 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-b-2xl"></div>
                                    </div>
                                );
                            })
                        )}
                    </div>
                )}

                {/* Message for non-approved providers */}
                {profileStatus !== 'approved' && !loading && (
                    <div className="text-center py-12">
                        <div className="bg-gray-800/50 rounded-2xl p-8 max-w-md mx-auto">
                            <Clock className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                            <h3 className="text-xl font-semibold text-gray-300 mb-2">
                                Dashboard Access Pending
                            </h3>
                            <p className="text-gray-400">
                                Your dashboard statistics will be available once your profile is approved by our admin team.
                            </p>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default Dashboard;