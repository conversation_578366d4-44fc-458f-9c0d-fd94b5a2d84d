import { <PERSON><PERSON> } from "primereact/button";
import { InputText } from "primereact/inputtext";
import { InputNumber } from 'primereact/inputnumber';
import { FormikHelpers, useFormik } from "formik";
import * as Yup from "yup";
import { catchAsync, handelFormData, handelResponse, showError } from "../../utils/helper";
import { useEffect, useState } from "react";
import { getProfile, setProfile } from "../../apis/provider";
import { daysOptions, defaultLocation, paymentOptions, Provider } from "../../types/global";
import GoogleMapComponent from "../../components/admin/GoogleMap";
import { useAuth } from "../../utils/AuthContext";
import { useNavigate } from "react-router-dom";

const Profile = () => {
    const { login, accessToken, role } = useAuth();
    const navigate = useNavigate();
    const providerEmail = localStorage.getItem("email")
    const [loading, setLoading] = useState<boolean>(false);
    const [profileStatus, setProfileStatus] = useState<'pending' | 'approved' | 'rejected' | null>(null);
    // Helper function to get file display URL
    const getFileDisplayUrl = (file: string | File | null): string | null => {
        if (!file) return null;
        if (typeof file === 'string') return file;
        if (file instanceof File) return URL.createObjectURL(file);
        return null;
    };

    // Helper function to check if file is PDF
    const isPdfFile = (file: string | File | null): boolean => {
        if (!file) return false;
        if (typeof file === 'string') return file.toLowerCase().includes('.pdf');
        if (file instanceof File) return file.type === 'application/pdf';
        return false;
    };

    const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>, field: string) => {
        const file = event.currentTarget.files?.[0] || null;

        if (file) {
            const isValidSize = file.size >= 20 * 1024 && file.size <= 2000 * 1024;
            const isValidType = ["image/jpeg", "image/jpg", "image/png", "application/pdf"].includes(file.type);

            if (!isValidSize) {
                showError("File size must be between 20KB and 2MB");
                return;
            }
            if (!isValidType) {
                showError("Only JPG, JPEG, PNG, or PDF files are allowed");
                return;
            }
        }

        formik.setFieldValue(field, file);
    };

    const formik = useFormik<Provider>({
        initialValues: {
           email: providerEmail ?? undefined,
            name: "",
            description: "",
            photoId: null,
            cannabisLicense: null,
            resellersPermit: null,
            street: "",
            city: "",
            state: "",
            country: "",
            zipCode: "",
            latitude: defaultLocation.latitude,
            longitude: defaultLocation.longitude,
            radius: defaultLocation.radius,
            paymentOption: [],
            startTime: "06:00",
            endTime: "23:00",
            availableDays: [],
        },
        validationSchema: Yup.object({
            // email: Yup.string().email("Invalid email format").optional(),
            name: Yup.string().required("Name is required"),
            description: Yup.string().required("Description is required"),
            photoId: Yup.mixed().nullable(),
            cannabisLicense: Yup.mixed().nullable(),
            resellersPermit: Yup.mixed().nullable(),
            street: Yup.string().required("Street is required"),
            city: Yup.string().required("City is required"),
            state: Yup.string().required("State is required"),
            country: Yup.string().required("Country is required"),
            zipCode: Yup.string()
                .matches(/^\d+$/, 'ZIP code must contain only numbers')
                .min(4, 'Too short')
                .max(10, 'Too long')
                .required('ZIP code is required'),
            latitude: Yup.number().required("Latitude is required"),
            longitude: Yup.number().required("Longitude is required"),
            radius: Yup.string().required("Radius is required"),
            paymentOption: Yup.array().of(Yup.string().oneOf(paymentOptions)).min(1, "At least one payment option is required").required(),
            startTime: Yup.string().matches(/^([01]?[0-9]|2[0-3]):([0-5][0-9])$/, "Start time must be in HH:mm format").required(),
            endTime: Yup.string().matches(/^([01]?[0-9]|2[0-3]):([0-5][0-9])$/, "End time must be in HH:mm format").required(),
            availableDays: Yup.array().of(Yup.string().oneOf(daysOptions)).min(1, "At least one day avilability is required").required("At least one available day is required"),
        }),
        onSubmit: async (values, actions: FormikHelpers<Provider>) => {

            setLoading(true);
            const formData = handelFormData(values);
            // if (formData) {
            //     console.log(formik.values)
            //     showError("Profile submitted");
            // } else {
            //     showError("Profile not submitted");
            // }

            catchAsync(
                async () => {
                    const response = await setProfile(formData);
                    handelResponse(response, actions);
                    // Handle successful profile submission
                    if (response.status) {
                        // Immediately update cached data with submitted values
                        const submittedData = { ...values };
                        // Remove file objects for caching (they can't be stringified)
                        const cacheData = {
                            ...submittedData,
                            photoId: typeof submittedData.photoId === 'string' ? submittedData.photoId : null,
                            cannabisLicense: typeof submittedData.cannabisLicense === 'string' ? submittedData.cannabisLicense : null,
                            resellersPermit: typeof submittedData.resellersPermit === 'string' ? submittedData.resellersPermit : null,
                        };
                        localStorage.setItem("providerProfileData", JSON.stringify(cacheData));

                        // Update profile status cache for instant dashboard updates
                        const profileWithStatus = {
                            ...cacheData,
                            isApproved: 'pending' // New/updated profiles are pending by default
                        };
                        localStorage.setItem("providerProfile", JSON.stringify(profileWithStatus));

                        // Refetch profile data to get the latest from server
                        await fetchProfileData();

                        // Update auth state to mark profile as created
                        const refreshToken = localStorage.getItem("refreshToken");
                        const accessExpires = localStorage.getItem("accessExpires");
                        const refreshExpires = localStorage.getItem("refreshExpires");

                        if (accessToken && refreshToken && accessExpires && refreshExpires && role) {
                            // Profile created successfully, set isProviderProfile to true
                            login(accessToken, refreshToken, accessExpires, refreshExpires, role, true, false);
                        }

                        // Navigate to dashboard after successful profile creation
                        navigate("/dashboard");
                    }
                }, { showToast: false }
            ).finally(() => setLoading(false));
        },
    });

    const fetchProfileData = async () => {
        setLoading(true);
        catchAsync(async () => {
            const response = await getProfile();
            if (response.status && response.data) {
                // Set profile status
                setProfileStatus(response.data.isApproved || 'pending');

                // Store full profile data including status for dashboard
                localStorage.setItem("providerProfile", JSON.stringify(response.data));

                // Create form data without system fields
                const formData = { ...response.data };
                const keysToRemove = ["_id", "isApproved", "deleted", "createdAt", "updatedAt", "__v"];
                keysToRemove.forEach(key => {
                    delete formData[key];
                });

                // Store form data for persistence
                localStorage.setItem("providerProfileData", JSON.stringify(formData));

                formik.setValues({
                    ...formData,
                    paymentOption: response.data.paymentOption || [],
                    availableDays: response.data.availableDays || [],
                });
            } else {
                // If no profile exists, check if we have cached data
                const cachedData = localStorage.getItem("providerProfileData");
                if (cachedData) {
                    const parsedData = JSON.parse(cachedData);
                    formik.setValues({
                        ...parsedData,
                        paymentOption: parsedData.paymentOption || [],
                        availableDays: parsedData.availableDays || [],
                    });
                }
            }
        }).finally(() => setLoading(false));
    };

    useEffect(() => {
        // Always try to fetch profile data, whether provider is active or not
        // This handles both existing providers and new providers
        fetchProfileData();
    }, []);

    // Check if profile is editable
    const isProfileEditable = profileStatus === 'approved' || profileStatus === null || profileStatus === 'pending';

    return (
        <div className="flex justify-center items-center ">
            <div className="bg-gray-800 rounded-2xl p-5 w-full max-w-lg">
                <h2 className="text-center text-2xl font-bold text-[#CE93D8] mb-4">Profile</h2>

                {/* Status Message for Non-Approved Providers */}
                {profileStatus === 'rejected' && (
                    <div className="mb-4 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                        <div className="flex items-center gap-3">
                            <i className="pi pi-times-circle text-red-400 text-lg"></i>
                            <div>
                                <h4 className="text-red-300 font-semibold">Profile Rejected</h4>
                                <p className="text-red-200 text-sm">Your profile has been rejected. Please contact admin for details.</p>
                            </div>
                        </div>
                    </div>
                )}

                {profileStatus === 'pending' && (
                    <div className="mb-4 p-4 bg-yellow-500/10 border border-yellow-500/20 rounded-lg">
                        <div className="flex items-center gap-3">
                            <i className="pi pi-clock text-yellow-400 text-lg"></i>
                            <div>
                                <h4 className="text-yellow-300 font-semibold">Profile Under Review</h4>
                                <p className="text-yellow-200 text-sm">Your profile is being reviewed. You can edit until approved.</p>
                            </div>
                        </div>
                    </div>
                )}

                {profileStatus === 'approved' && (
                    <div className="mb-4 p-4 bg-green-500/10 border border-green-500/20 rounded-lg">
                        <div className="flex items-center gap-3">
                            <i className="pi pi-check-circle text-green-400 text-lg"></i>
                            <div>
                                <h4 className="text-green-300 font-semibold">Profile Approved</h4>
                                <p className="text-green-200 text-sm">Your profile has been approved. You can update your information anytime.</p>
                            </div>
                        </div>
                    </div>
                )}

                <form onSubmit={formik.handleSubmit} encType="multipart/form-data" className="space-y-4">
                    <div>
                        <label htmlFor="email" className="block text-sm font-medium">
                            Email
                        </label>
                        <InputText
                            id="email"
                            name="email"
                            value={providerEmail}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            disabled={true}
                            className="w-full mt-2 p-2 border rounded-md"
                        />
                        {formik.touched.email && formik.errors.email && <small className="text-red-500">{formik.errors.email}</small>}
                    </div>
                    <div>
                        <label htmlFor="name" className="block text-sm font-medium">
                            Name
                        </label>
                        <InputText
                            id="name"
                            name="name"
                            value={formik.values.name}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            disabled={!isProfileEditable}
                            className="w-full mt-2 p-2 border rounded-md"
                        />
                        {formik.touched.name && formik.errors.name && <small className="text-red-500">{formik.errors.name}</small>}
                    </div>

                    <div>
                        <label htmlFor="description" className="block text-sm font-medium">
                            Description
                        </label>
                        <InputText
                            id="description"
                            name="description"
                            value={formik.values.description}
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            className="w-full mt-2 p-2 border rounded-md"
                        />
                        {formik.touched.description && formik.errors.description && <small className="text-red-500">{formik.errors.description}</small>}
                    </div>

                    {/* Photo ID Upload */}
                    <div>
                        <label htmlFor="photoId" className="block text-sm font-medium">Photo ID</label>

                        {/* Show file input for new uploads or if no existing file */}
                        {!formik.values.photoId ? (
                            <>
                                <input
                                    type="file"
                                    id="photoId"
                                    name="photoId"
                                    accept="image/jpeg, image/jpg, image/png, application/pdf"
                                    onChange={(e) => handleFileChange(e, "photoId")}
                                    disabled={!isProfileEditable}
                                    className="w-full mt-2 p-2 border rounded-md"
                                />
                                {formik.touched.photoId && formik.errors.photoId && <small className="text-red-500">{formik.errors.photoId}</small>}
                            </>
                        ) : (
                            /* Show uploaded status with image preview */
                            <div className="mt-2">
                                <div className="flex items-center justify-between gap-3 p-3 bg-gray-800 rounded-md border">
                                    <div
                                        className="cursor-pointer hover:opacity-80 transition-opacity"
                                        onClick={() => document.getElementById('photoId-hidden')?.click()}
                                        title="Click to change file"
                                    >
                                            {isPdfFile(formik.values.photoId) ? (
                                                <div className="w-10 h-10 bg-red-100 rounded border flex items-center justify-center">
                                                    <i className="pi pi-file-pdf text-red-600"></i>
                                                </div>
                                            ) : (
                                                <img
                                                    src={getFileDisplayUrl(formik.values.photoId) || ''}
                                                    alt="Photo ID"
                                                    className="w-10 h-10 object-cover rounded border"
                                                />
                                            )}
                                    </div>
                                    <Button
                                        icon="pi pi-eye"
                                        className="p-button-text p-button-sm"
                                        onClick={() => {
                                            const url = getFileDisplayUrl(formik.values.photoId);
                                            if (url) window.open(url, '_blank');
                                        }}
                                        type="button"
                                        tooltip="View in new tab"
                                    />
                                </div>
                                {/* Hidden file input for replacement */}
                                <input
                                    type="file"
                                    id="photoId-hidden"
                                    name="photoId"
                                    accept="image/jpeg, image/jpg, image/png, application/pdf"
                                    onChange={(e) => handleFileChange(e, "photoId")}
                                    className="hidden"
                                />
                            </div>
                        )}
                    </div>
                    {/* Cannabis License Upload */}
                    <div>
                        <label htmlFor="cannabisLicense" className="block text-sm font-medium">Cannabis License</label>

                        {/* Show file input for new uploads or if no existing file */}
                        {!formik.values.cannabisLicense ? (
                            <>
                                <input
                                    type="file"
                                    id="cannabisLicense"
                                    name="cannabisLicense"
                                    accept="image/jpeg, image/jpg, image/png, application/pdf"
                                    onChange={(e) => handleFileChange(e, "cannabisLicense")}
                                    className="w-full mt-2 p-2 border rounded-md"
                                />
                                {formik.touched.cannabisLicense && formik.errors.cannabisLicense && <small className="text-red-500">{formik.errors.cannabisLicense}</small>}
                            </>
                        ) : (
                            /* Show image preview */
                            <div className="mt-2">
                                <div className="flex items-center justify-between gap-3 p-3 bg-gray-800 rounded-md border">
                                    <div
                                        className="cursor-pointer hover:opacity-80 transition-opacity"
                                        onClick={() => document.getElementById('cannabisLicense-hidden')?.click()}
                                        title="Click to change file"
                                    >
                                            {isPdfFile(formik.values.cannabisLicense) ? (
                                                <div className="w-10 h-10 bg-red-100 rounded border flex items-center justify-center">
                                                    <i className="pi pi-file-pdf text-red-600"></i>
                                                </div>
                                            ) : (
                                                <img
                                                    src={getFileDisplayUrl(formik.values.cannabisLicense) || ''}
                                                    alt="Cannabis License"
                                                    className="w-10 h-10 object-cover rounded border"
                                                />
                                            )}
                                    </div>
                                    <Button
                                        icon="pi pi-eye"
                                        className="p-button-text p-button-sm"
                                        onClick={() => {
                                            const url = getFileDisplayUrl(formik.values.cannabisLicense);
                                            if (url) window.open(url, '_blank');
                                        }}
                                        type="button"
                                        tooltip="View in new tab"
                                    />
                                </div>
                                {/* Hidden file input for replacement */}
                                <input
                                    type="file"
                                    id="cannabisLicense-hidden"
                                    name="cannabisLicense"
                                    accept="image/jpeg, image/jpg, image/png, application/pdf"
                                    onChange={(e) => handleFileChange(e, "cannabisLicense")}
                                    className="hidden"
                                />
                            </div>
                        )}
                    </div>

                    {/* Resellers Permit Upload */}
                    <div>
                        <label htmlFor="resellersPermit" className="block text-sm font-medium">Resellers Permit</label>

                        {/* Show file input for new uploads or if no existing file */}
                        {!formik.values.resellersPermit ? (
                            <>
                                <input
                                    type="file"
                                    id="resellersPermit"
                                    name="resellersPermit"
                                    accept="image/jpeg, image/jpg, image/png, application/pdf"
                                    onChange={(e) => handleFileChange(e, "resellersPermit")}
                                    className="w-full mt-2 p-2 border rounded-md"
                                />
                                {formik.touched.resellersPermit && formik.errors.resellersPermit && <small className="text-red-500">{formik.errors.resellersPermit}</small>}
                            </>
                        ) : (
                            /* Show image preview */
                            <div className="mt-2">
                                <div className="flex items-center justify-between gap-3 p-3 bg-gray-800 rounded-md border">
                                    <div
                                        className="cursor-pointer hover:opacity-80 transition-opacity"
                                        onClick={() => document.getElementById('resellersPermit-hidden')?.click()}
                                        title="Click to change file"
                                    >
                                            {isPdfFile(formik.values.resellersPermit) ? (
                                                <div className="w-10 h-10 bg-red-100 rounded border flex items-center justify-center">
                                                    <i className="pi pi-file-pdf text-red-600"></i>
                                                </div>
                                            ) : (
                                                <img
                                                    src={getFileDisplayUrl(formik.values.resellersPermit) || ''}
                                                    alt="Resellers Permit"
                                                    className="w-10 h-10 object-cover rounded border"
                                                />
                                            )}
                                    </div>
                                    <Button
                                        icon="pi pi-eye"
                                        className="p-button-text p-button-sm"
                                        onClick={() => {
                                            const url = getFileDisplayUrl(formik.values.resellersPermit);
                                            if (url) window.open(url, '_blank');
                                        }}
                                        type="button"
                                        tooltip="View in new tab"
                                    />
                                </div>
                                {/* Hidden file input for replacement */}
                                <input
                                    type="file"
                                    id="resellersPermit-hidden"
                                    name="resellersPermit"
                                    accept="image/jpeg, image/jpg, image/png, application/pdf"
                                    onChange={(e) => handleFileChange(e, "resellersPermit")}
                                    className="hidden"
                                />
                            </div>
                        )}
                    </div>

                    {/* Other fields (Address, Payment options, etc.) */}
                    <div className="grid grid-cols-2 mx-0 justify-between">
                        <div>
                            <label htmlFor="street" className="block text-sm font-medium">Street</label>
                            <InputText
                                id="street"
                                name="street"
                                value={formik.values.street}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                className="w-full mt-2 p-2 border rounded-md"
                            />
                            {formik.touched.street && formik.errors.street && <small className="text-red-500">{formik.errors.street}</small>}
                        </div>
                        <div>
                            <label htmlFor="city" className="block text-sm font-medium">City</label>
                            <InputText
                                id="city"
                                name="city"
                                value={formik.values.city}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                className="w-full mt-2 p-2 border rounded-md"
                            />
                            {formik.touched.city && formik.errors.city && <small className="text-red-500">{formik.errors.city}</small>}
                        </div>
                    </div>

                    <div className="grid grid-cols-2 mx-0 justify-between">
                        <div>
                            <label htmlFor="state" className="block text-sm font-medium">State</label>
                            <InputText
                                id="state"
                                name="state"
                                value={formik.values.state}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                className="w-full mt-2 p-2 border rounded-md"
                            />
                            {formik.touched.state && formik.errors.state && <small className="text-red-500">{formik.errors.state}</small>}
                        </div>
                        <div>
                            <label htmlFor="country" className="block text-sm font-medium">Country</label>
                            <InputText
                                id="country"
                                name="country"
                                value={formik.values.country}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                className="w-full mt-2 p-2 border rounded-md"
                            />
                            {formik.touched.country && formik.errors.country && <small className="text-red-500">{formik.errors.country}</small>}
                        </div>
                    </div>

                    <div className="grid grid-cols-2 mx-0 justify-between">
                        <div>
                            <label htmlFor="zipCode" className="block text-sm font-medium">Zip Code</label>
                            <InputText
                                id="zipCode"
                                name="zipCode"
                                placeholder="Enter ZIP code (e.g. 10001)"
                                type="text"
                                keyfilter="int"
                                value={formik.values.zipCode}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                className="w-full mt-2 p-2 border rounded-md"
                            />
                            {formik.touched.zipCode && formik.errors.zipCode && <small className="text-red-500">{formik.errors.zipCode}</small>}
                        </div>
                        <div>
                            <label htmlFor="radius" className="block text-sm font-medium">Radius</label>
                            <InputNumber
                                id="radius"
                                name="radius"
                                value={formik.values.radius}
                                onValueChange={(e) => formik.setFieldValue('radius', e.value ?? 0)}
                                onBlur={formik.handleBlur}
                                useGrouping={false}
                                className="w-full mt-2 rounded-md"
                            />
                            {formik.touched.radius && formik.errors.radius && (
                                <small className="p-error">{formik.errors.radius as string}</small>
                            )}
                        </div>
                    </div>

                    {/* Location and Radius */}
                    <div className="grid grid-cols-2 px-2">
                        <label className="block text-sm font-medium">Location</label>
                        <div className="flex justify-between w-full">
                            <p className="text-sm font-medium">
                                Lat: <span className="ms-3 text-[#CE93D8]">{formik.values.latitude}</span>
                            </p>
                            <p className="text-sm font-medium">
                                Lng: <span className="ms-3 text-[#CE93D8]">{formik.values.longitude}</span>
                            </p>
                        </div>
                        <div className="relative block w-full">
                            <GoogleMapComponent
                                radius={formik.values.radius || defaultLocation.radius}
                                lat={formik.values.latitude || defaultLocation.latitude}
                                lng={formik.values.longitude || defaultLocation.longitude}
                                editable={true}
                                onLocationChange={(lat, lng) => {
                                    formik.setFieldValue('latitude', lat);
                                    formik.setFieldValue('longitude', lng);
                                }}
                            />
                        </div>
                    </div>

                    {/* Payment Options */}
                    <div>
                        <label htmlFor="paymentOption" className="block text-sm font-medium">
                            Payment Options
                        </label>
                        <div className="flex flex-wrap mt-2">
                            {['Credit Card', 'UPI', 'Debit Card', 'Net Banking'].map((option) => (
                                <div key={option} className="mr-4 mb-2">
                                    <input
                                        className="mr-2"
                                        type="checkbox"
                                        id={`paymentOption-${option}`}
                                        name="paymentOption"
                                        value={option}
                                        checked={formik.values.paymentOption?.includes(option) || false}
                                        onChange={(e) => {
                                            const value = e.target.value;
                                            const isChecked = e.target.checked;
                                            const currentOptions = formik.values.paymentOption || [];
                                            const newOptions = isChecked
                                                ? [...currentOptions, value]
                                                : currentOptions.filter(opt => opt !== value);
                                            formik.setFieldValue('paymentOption', newOptions);
                                        }}
                                    />
                                    <label htmlFor={`paymentOption-${option}`} className="text-sm">{option}</label>
                                </div>
                            ))}
                        </div>
                        {formik.touched.paymentOption && formik.errors.paymentOption ? (
                            <small className="text-red-500">{formik.errors.paymentOption}</small>
                        ) : null}
                    </div>

                    {/* Time (Start Time & End Time) */}
                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <label htmlFor="startTime" className="block text-sm font-medium">
                                Start Time
                            </label>
                            <InputText
                                id="startTime"
                                name="startTime"
                                type="time"
                                value={formik.values.startTime}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                className="w-full mt-2 p-2 border rounded-md"
                            />
                            {formik.touched.startTime && formik.errors.startTime ? (
                                <small className="text-red-500">{formik.errors.startTime}</small>
                            ) : null}
                        </div>

                        <div>
                            <label htmlFor="endTime" className="block text-sm font-medium">
                                End Time
                            </label>
                            <InputText
                                id="endTime"
                                name="endTime"
                                type="time"
                                value={formik.values.endTime}
                                onChange={formik.handleChange}
                                onBlur={formik.handleBlur}
                                className="w-full mt-2 p-2 border rounded-md"
                            />
                            {formik.touched.endTime && formik.errors.endTime ? (
                                <small className="text-red-500">{formik.errors.endTime}</small>
                            ) : null}
                        </div>
                    </div>

                    {/* Available Days */}
                    <div>
                        <label htmlFor="availableDays" className="block text-sm font-medium">
                            Available Days
                        </label>
                        <div className="flex flex-wrap mt-2">
                            {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map((day) => (
                                <div key={day} className="mr-4 mb-2">
                                    <input
                                        type="checkbox"
                                        id={`availableDays-${day}`}
                                        name="availableDays"
                                        value={day}
                                        checked={formik.values.availableDays?.includes(day) || false}
                                        className="mr-2"
                                        onChange={(e) => {
                                            const day = e.target.value;
                                            const isChecked = e.target.checked;
                                            const currentDays = formik.values.availableDays || [];
                                            const newDays = isChecked
                                                ? [...currentDays, day]
                                                : currentDays.filter(d => d !== day);
                                            formik.setFieldValue('availableDays', newDays);
                                        }}
                                    />
                                    <label htmlFor={`availableDays-${day}`} className="text-sm">{day}</label>
                                </div>
                            ))}
                        </div>
                        {formik.touched.availableDays && formik.errors.availableDays ? (
                            <small className="text-red-500">{formik.errors.availableDays}</small>
                        ) : null}
                    </div>

                    <Button
                        type="submit"
                        label={profileStatus === 'rejected' ? "Profile Editing Disabled" : "Update Profile"}
                        className="w-full "
                        disabled={loading || !isProfileEditable}
                    />
                </form>
            </div>
        </div>
    );
};

export default Profile;
