import React from 'react';
import { <PERSON>ik<PERSON><PERSON><PERSON>, useFormik } from 'formik';
import * as Yup from 'yup';
import { Dialog } from 'primereact/dialog';
import { InputText } from 'primereact/inputtext';
import { Password } from 'primereact/password';
import { <PERSON><PERSON> } from 'primereact/button';
import GoogleMapComponent from './GoogleMap';
import { CustomerFormData, defaultLocation } from '../../types/global';

interface CustomerFormProps {
  visible: boolean;
  onHide: () => void;
  onSubmit: (values: CustomerFormData, actions: FormikHelpers<CustomerFormData>) => void;
  initialValues: CustomerFormData;
  editMode: boolean;
}

const CustomerForm: React.FC<CustomerFormProps> = ({
  visible,
  onHide,
  onSubmit,
  initialValues,
  editMode
}) => {
  const validationSchema = Yup.object({
    email: Yup.string().email('Invalid email format').required('Email is required'),
    username: Yup.string().required('Userna<PERSON> is required'),
    password: editMode ? Yup.string() : Yup.string().required('Password is required'),
    photo: Yup.mixed().nullable(),
    bio: Yup.string().max(500, 'Bio must be 500 characters or less').nullable(),
    emailPermission: Yup.boolean().required(),
    notificationPermission: Yup.boolean().required(),
    latitude: Yup.number().nullable(),
    longitude: Yup.number().nullable(),
    address: Yup.object({
      pincode: Yup.string().nullable(),
      locality: Yup.string().nullable(),
      address_line: Yup.string().nullable(),
      city: Yup.string().nullable(),
      state: Yup.string().nullable(),
      landmark: Yup.string().nullable()
    }).nullable()
  });

  const formik = useFormik<CustomerFormData>({
    initialValues,
    validationSchema,
    onSubmit: (values, actions) => {
      onSubmit(values, actions);
    },
    enableReinitialize: true
  });

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      if (file.size < 20 * 1024 || file.size > 2000 * 1024) {
        alert('File size must be between 20KB and 2MB');
        return;
      }
      if (!['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/svg+xml'].includes(file.type)) {
        alert('Invalid file type');
        return;
      }
      formik.setFieldValue('photo', file);
    }
  };

  return (
    <Dialog
      header={editMode ? 'Edit Customer' : 'Create Customer'}
      visible={visible}
      style={{ width: '50vw' }}
      modal
      appendTo={document.body}
      className="p-fluid"
      onHide={onHide}
    >
      <form onSubmit={formik.handleSubmit} className="p-fluid">
        <div className="field">
          <label htmlFor="email">Email</label>
          <InputText
            id="email"
            name="email"
            value={formik.values.email}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            autoComplete="email"
          />
          {formik.touched.email && formik.errors.email && (
            <small className="p-error">{formik.errors.email}</small>
          )}
        </div>

        {!editMode && (
          <div className="field">
            <label htmlFor="password">Password</label>
            <Password
              id="password"
              name="password"
              value={formik.values.password}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              toggleMask
              feedback={false}
              autoComplete="new-password"
            />
            {formik.touched.password && formik.errors.password && (
              <small className="p-error">{formik.errors.password}</small>
            )}
          </div>
        )}

        <div className="field">
          <label htmlFor="username">Username</label>
          <InputText
            id="username"
            name="username"
            value={formik.values.username}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            autoComplete="username"
          />
          {formik.touched.username && formik.errors.username && (
            <small className="p-error">{formik.errors.username}</small>
          )}
        </div>

        <div className="field">
          <label htmlFor="photo">Profile Photo</label>
          <input
            type="file"
            id="photo"
            name="photo"
            accept="image/*"
            onChange={handleFileChange}
            className="p-inputtext p-component"
            autoComplete="off"
          />
        </div>

        <div className="field">
          <label htmlFor="bio">Bio</label>
          <InputText
            id="bio"
            name="bio"
            value={formik.values.bio || ''}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            autoComplete="off"
          />
          {formik.touched.bio && formik.errors.bio && (
            <small className="p-error">{formik.errors.bio}</small>
          )}
        </div>

        <div className="field">
          <label>Email Permission</label>
          <div className="flex align-items-center">
            <input
              type="checkbox"
              id="emailPermission"
              name="emailPermission"
              checked={formik.values.emailPermission}
              onChange={formik.handleChange}
              className="mr-2"
              autoComplete="off"
            />
            <label htmlFor="emailPermission">Allow marketing emails</label>
          </div>
        </div>

        <div className="field">
          <label>Notification Permission</label>
          <div className="flex align-items-center">
            <input
              type="checkbox"
              id="notificationPermission"
              name="notificationPermission"
              checked={formik.values.notificationPermission}
              onChange={formik.handleChange}
              className="mr-2"
              autoComplete="off"
            />
            <label htmlFor="notificationPermission">Allow push notifications</label>
          </div>
        </div>

        {/* Address Fields */}
        <div className="field">
          <label htmlFor="address.phone">Phone</label>
          <InputText
            id="address.phone"
            name="address.phone"
            value={formik.values.address?.phone || ''}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            autoComplete="tel"
          />
          {formik.touched.address?.phone && formik.errors.address?.phone && (
            <small className="p-error">{formik.errors.address.phone}</small>
          )}
        </div>

        <div className="field">
          <label htmlFor="address.pincode">Pincode</label>
          <InputText
            id="address.pincode"
            name="address.pincode"
            value={formik.values.address?.pincode || ''}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            autoComplete="postal-code"
          />
          {formik.touched.address?.pincode && formik.errors.address?.pincode && (
            <small className="p-error">{formik.errors.address.pincode}</small>
          )}
        </div>

        <div className="field">
          <label htmlFor="address.locality">Locality</label>
          <InputText
            id="address.locality"
            name="address.locality"
            value={formik.values.address?.locality || ''}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            autoComplete="address-level2"
          />
          {formik.touched.address?.locality && formik.errors.address?.locality && (
            <small className="p-error">{formik.errors.address.locality}</small>
          )}
        </div>

        <div className="field">
          <label htmlFor="address.address_line">Address Line</label>
          <InputText
            id="address.address_line"
            name="address.address_line"
            value={formik.values.address?.address_line || ''}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            autoComplete="street-address"
          />
          {formik.touched.address?.address_line && formik.errors.address?.address_line && (
            <small className="p-error">{formik.errors.address.address_line}</small>
          )}
        </div>

        <div className="field">
          <label htmlFor="address.city">City</label>
          <InputText
            id="address.city"
            name="address.city"
            value={formik.values.address?.city || ''}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            autoComplete="address-level1"
          />
          {formik.touched.address?.city && formik.errors.address?.city && (
            <small className="p-error">{formik.errors.address.city}</small>
          )}
        </div>

        <div className="field">
          <label htmlFor="address.state">State</label>
          <InputText
            id="address.state"
            name="address.state"
            value={formik.values.address?.state || ''}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            autoComplete="address-level1"
          />
          {formik.touched.address?.state && formik.errors.address?.state && (
            <small className="p-error">{formik.errors.address.state}</small>
          )}
        </div>

        <div className="field">
          <label htmlFor="address.landmark">Landmark</label>
          <InputText
            id="address.landmark"
            name="address.landmark"
            value={formik.values.address?.landmark || ''}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            autoComplete="off"
          />
          {formik.touched.address?.landmark && formik.errors.address?.landmark && (
            <small className="p-error">{formik.errors.address.landmark}</small>
          )}
        </div>

        <div className="field">
          <label>Location</label>
          <GoogleMapComponent
            lat={formik.values.latitude || defaultLocation.latitude}
            lng={formik.values.longitude || defaultLocation.longitude}
            editable={true}
            onLocationChange={(lat, lng) => {
              formik.setFieldValue('latitude', lat);
              formik.setFieldValue('longitude', lng);
            }}
            onAddressSelect={(address) => {
              console.log("address", address)
              formik.setFieldValue('address.pincode', address.pincode);
              formik.setFieldValue('address.locality', address.locality);
              formik.setFieldValue('address.address_line', address.address_line);
              formik.setFieldValue('address.city', address.city);
              formik.setFieldValue('address.state', address.state);
              formik.setFieldValue('address.landmark', address.landmark);
            }}
          />
        </div>

        <Button
          type="submit"
          label={editMode ? 'Update Customer' : 'Create Customer'}
          className="mt-4"
        />
      </form>
    </Dialog>
  );
};

export default CustomerForm;
