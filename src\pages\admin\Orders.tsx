import React, { useState, useEffect } from 'react';
import { DataTable } from 'primereact/datatable';
import { InputText } from 'primereact/inputtext';
import { Column } from 'primereact/column';
import { IconField } from 'primereact/iconfield';
import { InputIcon } from 'primereact/inputicon';
import { Tag } from 'primereact/tag';
import { ConfirmDialog } from 'primereact/confirmdialog';
import usePagination from '../../hooks/usePagination';
import { getOrders} from '../../apis/admin';
import { catchAsync } from '../../utils/helper';
import { Order, defaultPaginationValues } from '../../types/global';

const Orders: React.FC = () => {
    const [orders, setOrders] = useState<Order[]>([]);
    const [totalRecords, setTotalRecords] = useState(0);
    const [loading, setLoading] = useState(false);
    const { params, onPage, onSort, setSearch } = usePagination(defaultPaginationValues);
    const [searchText, setSearchText] = useState('');



    useEffect(() => {
        fetchOrders();
    }, [params]);

    const fetchOrders = async () => {
        setLoading(true);
        catchAsync(async () => {
            const response = await getOrders({
                page: params.page,
                // limit: params.limit,
                // sort: params.sortField && params.sortOrder
                //     ? `${params.sortField}:${params.sortOrder === 1 ? 'asc' : 'desc'}`
                //     : undefined,
                // search: params.search,
            });
            setOrders(response.data.results);
            setTotalRecords(response.data.totalResults);
        }).finally(() => setLoading(false));
    };

    const handleSearch = () => {
        setSearch(searchText);
    };

    // Template functions
    const statusBodyTemplate = (rowData: Order) => {
        const getSeverity = (status: string) => {
            switch (status) {
                case 'pending': return 'warning';
                case 'confirmed': return 'info';
                case 'preparing': return 'secondary';
                case 'ready': return 'success';
                case 'delivered': return 'success';
                case 'cancelled': return 'danger';
                case 'cancelled_by_user': return 'danger';
                default: return 'info';
            }
        };

        const getDisplayValue = (status: string) => {
            return status === 'cancelled_by_user' ? 'Cancelled by User' : status.charAt(0).toUpperCase() + status.slice(1);
        };

        return <Tag value={getDisplayValue(rowData.status)} severity={getSeverity(rowData.status)} />;
    };


    const priceBodyTemplate = (rowData: Order) => {
        return `$${rowData.price.toFixed(2)}`;
    };

    const shippingChargesBodyTemplate = (rowData: Order) => {
        return rowData.shipping_charges ? `$${rowData.shipping_charges.toFixed(2)}` : 'Free';
    };

    const variantBodyTemplate = (rowData: Order) => {
        return rowData.variant_flavour || 'Default';
    };

    const orderDateBodyTemplate = (rowData: Order) => {
        return new Date(rowData.order_date).toLocaleDateString();
    };

    const providerBodyTemplate = (rowData: Order) => {
        return rowData.provider_name || 'N/A';
    };

    return (
        <div>
            <div className="flex justify-between  align-items-center">
                    <h2>All Orders</h2>
                       <div className="flex justify-between mb-4">
                    <IconField iconPosition="left">
                        <InputIcon className="pi pi-search" />
                        <InputText
                            value={searchText}
                            onChange={(e) => setSearchText(e.target.value)}
                            placeholder="Search orders..."
                            onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                        />
                    </IconField>

                </div>
            </div>

            <div className=" rounded-lg shadow">
             

                <DataTable
                    value={orders}
                    lazy
                    paginator
                    first={(params.page - 1) * params.limit}
                    rows={params.limit}
                    totalRecords={totalRecords}
                    onPage={onPage}
                    onSort={onSort}
                    sortField={params.sortField}
                    sortOrder={params.sortOrder}
                    loading={loading}
                    rowsPerPageOptions={[10, 20, 50]}
                    className="p-datatable-striped"
                    removableSort
                >
                    <Column field="order_id" header="Order ID" sortable />
                    <Column field="product_name" header="Product" sortable />
                    <Column header="Variant" body={variantBodyTemplate} />
                    <Column field="quantity" header="Quantity" sortable />
                    <Column header="Price" body={priceBodyTemplate} sortable />
                    <Column header="Status" body={statusBodyTemplate} sortable />
                    <Column header="Order Date" body={orderDateBodyTemplate} sortable />
                    <Column header="Provider" body={providerBodyTemplate} sortable />
                    <Column field="customer_email" header="Customer Email" sortable />
                    <Column header="Shipping" body={shippingChargesBodyTemplate} />
                </DataTable>
            </div>

            <ConfirmDialog />
        </div>
    );
};

export default Orders;
