import React, { useState, useEffect } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { Button } from 'primereact/button';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { Dialog } from 'primereact/dialog';
import { IconField } from 'primereact/iconfield';
import { InputIcon } from 'primereact/inputicon';
import usePagination from '../../hooks/usePagination';
import { defaultLocation, defaultPaginationValues } from '../../types/global';
import { catchAsync, handelFormData, handelResponse } from '../../utils/helper';
import GoogleMapComponent from '../../components/admin/GoogleMap';
import {
    getCustomers,
    createCustomer,
    updateCustomer,
    deleteCustomer
} from '../../apis/admin';
import { Customer, CustomerFormData } from '../../types/global';
import CustomerForm from '../../components/admin/CustomerForm';
import { FormikHelpers } from 'formik';


const Customers: React.FC = () => {
    const [customers, setCustomers] = useState<Customer[]>([]);
    const [totalRecords, setTotalRecords] = useState(0);
    const [loading, setLoading] = useState(false);
    const { params, onPage, onSort, setSearch } = usePagination(defaultPaginationValues);
    const [searchText, setSearchText] = useState('');
    const [visibleDialog, setVisibleDialog] = useState(false);
    const [editMode, setEditMode] = useState(false);
    const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
    const [formData, setFormData] = useState<CustomerFormData>({
        email: '',
        username: '',
        bio: '',
        photo: '',
        password: '',
        emailPermission: true,
        notificationPermission: true,
        latitude: defaultLocation.latitude,
        longitude: defaultLocation.longitude,
        address: {
            phone: '',
            pincode: '',
            locality: '',
            address_line: '',
            city: '',
            state: '',
            landmark: '',
        },
    });
    const [locationPopupVisible, setLocationPopupVisible] = useState(false);


    useEffect(() => {
        fetchCustomers();
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [params]);



    const fetchCustomers = async () => {
        setLoading(true);
        catchAsync(async () => {
            const response = await getCustomers({
                page: params.page,
                limit: params.limit,
                sort:
                    params.sortField && params.sortOrder
                        ? `${params.sortField}:${params.sortOrder === 1 ? 'asc' : 'desc'}`
                        : undefined,
                search: params.search
            });
            setCustomers(response.data.results);
            setTotalRecords(response.data.totalResults);
        }).finally(() => setLoading(false));
    };



    const handleCreate = () => {
        setSelectedCustomer(null);
        setFormData({
            email: '',
            username: '',
            bio: '',
            photo: '',
            password: '',
            emailPermission: true,
            notificationPermission: true,
            latitude: defaultLocation.latitude,
            longitude: defaultLocation.longitude,
            address: {
                phone: '',
                pincode: '',
                locality: '',
                address_line: '',
                city: '',
                state: '',
                landmark: '',
            },
        });
        setEditMode(false);
        setVisibleDialog(true);
    };

    const handleEdit = (customer: Customer) => {
        console.log('=== EDITING CUSTOMER ===');
        console.log('Customer data:', customer);
        console.log('Customer address:', customer.address);
        console.log('Customer location:', { lat: customer.latitude, lng: customer.longitude });

        const formDataToSet = {
            _id: customer._id,
            email: customer.email,
            username: customer.username,
            photo: customer.photo,
            bio: customer.bio,
            emailPermission: customer.emailPermission,
            notificationPermission: customer.notificationPermission,
            latitude: customer.latitude,
            longitude: customer.longitude,
            address: {
                phone: customer.address?.phone || '',
                pincode: customer.address?.pincode || '',
                locality: customer.address?.locality || '',
                address_line: customer.address?.address_line || '',
                city: customer.address?.city || '',
                state: customer.address?.state || '',
                landmark: customer.address?.landmark || '',
            }
        };

        console.log('Form data being set:', formDataToSet);
        console.log('Address being set:', formDataToSet.address);

        setSelectedCustomer(customer);
        setFormData(formDataToSet);
        setEditMode(true);
        setVisibleDialog(true);
    };

    const handleDelete = (customerId: string) => {
        confirmDialog({
            message: 'Are you sure you want to delete this customer?',
            header: 'Confirmation',
            icon: 'pi pi-exclamation-triangle',
            accept: () => {
                catchAsync(async () => {
                    const response = await deleteCustomer(customerId);
                    handelResponse(response);
                    if (response.status) fetchCustomers();
                }, { showToast: true });
            }
        });
    };


        const handleSubmit = async (values: CustomerFormData, actions: FormikHelpers<CustomerFormData>) => {
        console.log('=== CUSTOMER FORM SUBMIT ===');
        console.log('Form values:', values);
        console.log('Edit mode:', editMode);
        console.log('Selected customer ID:', selectedCustomer?._id);

        // Use the same pattern as provider update
        const formDataToSend = handelFormData(values);

        catchAsync(async () => {
            let response;
            if (editMode && selectedCustomer?._id) {
                // Add customer ID for update (same pattern as provider)
                formDataToSend.append('customer', String(selectedCustomer._id));

                // Remove password if it exists (same as provider)
                if (formDataToSend.has('password')) {
                    formDataToSend.delete('password');
                }

                console.log('=== UPDATE REQUEST ===');
                console.log('Customer ID being sent:', selectedCustomer._id);

                // Log all FormData entries
                console.log('FormData entries:');
                for (let [key, value] of formDataToSend.entries()) {
                    console.log(`${key}:`, value);
                }

                response = await updateCustomer(formDataToSend);
            } else {
                console.log('=== CREATE REQUEST ===');
                response = await createCustomer(formDataToSend);
            }

            console.log('API Response:', response);
            handelResponse(response, actions);
            if (response.status) {
                setVisibleDialog(false);
                fetchCustomers();
            }
        }, { showToast: true });
    };
    useEffect(() => {
        const timer = setTimeout(() => {
            setSearch(searchText);
        }, 500);
        return () => clearTimeout(timer);
    }, [searchText]);

    const actionBodyTemplate = (rowData: Customer) => (
        <div className="flex gap-2">
            <Button
                icon="pi pi-pencil"
                className="p-button-rounded p-button-success w-6 h-8"
                onClick={() => handleEdit(rowData)}
            />
            <Button
                icon="pi pi-trash"
                className="p-button-rounded p-button-danger w-6 h-8"
                onClick={() => handleDelete(rowData._id)}
            />
        </div>
    );

    const permissionBodyTemplate = (rowData: Customer) => (
        <div className="flex flex-col gap-1">
            <span className={`p-tag p-tag-${rowData.emailPermission ? 'success' : 'danger'}`}>
                Email: {rowData.emailPermission ? 'Allowed' : 'Blocked'}
            </span>
            <span className={`p-tag p-tag-${rowData.notificationPermission ? 'success' : 'danger'}`}>
                Notifications: {rowData.notificationPermission ? 'Allowed' : 'Blocked'}
            </span>
        </div>
    );

    return (
        <div className="card p-fluid">
            <ConfirmDialog />

            <div className="flex justify-content-between align-items-center mb-4">
                <h2>Customers Management</h2>
                <div className="flex gap-2">
                    <IconField iconPosition="left">
                        <InputIcon className="pi pi-search" />
                        <InputText
                            value={searchText}
                            onChange={(e) => setSearchText(e.target.value)}
                            placeholder="Search customers..."
                        />
                    </IconField>
                    <Button label="Create Customer" icon="pi pi-plus" onClick={handleCreate} />
                </div>
            </div>

            <DataTable
                value={customers}
                lazy
                paginator
                first={(params.page - 1) * params.limit}
                rows={params.limit}
                totalRecords={totalRecords}
                onPage={onPage}
                onSort={onSort}
                sortField={params.sortField}
                sortOrder={params.sortOrder}
                loading={loading}
                rowsPerPageOptions={[10, 20, 50]}
                className="p-datatable-striped"
                removableSort
            >
                <Column field="username" header="Username" sortable />
                <Column field="email" header="Email" sortable />

                <Column
                    field="bio"
                    header="Bio"
                    body={(rowData) => (
                        <span>{rowData.bio || '-'}</span>  // ✅ Safe inside <td>
                    )}
                />

                <Column
                    header="Permissions"
                    body={permissionBodyTemplate}  // ✅ Make sure this returns only <span> or inline tags
                />

                <Column
                    header="Location"
                    body={(rowData) => (
                        <span
                            onClick={() => {
                                setSelectedCustomer(rowData); // this was missing for correct popup data
                                setLocationPopupVisible(true);
                            }}
                            className="cursor-pointer text-blue-600 hover:underline"
                        >
                            {/* ✅ Safely render address object as a string */}
                            {rowData.address
                                ? `${rowData.address.address_line}, ${rowData.address.city}, ${rowData.address.state}`
                                : 'No address'}
                        </span>
                    )}
                />

                <Column field="createdAt" header="Joined" sortable />

                <Column
                    header="Actions"
                    body={actionBodyTemplate}  // ✅ Ensure this doesn't return <div> or <p> inside
                />
            </DataTable>


            <CustomerForm
                visible={visibleDialog}
                onHide={() => setVisibleDialog(false)}
                onSubmit={handleSubmit}
                initialValues={formData}
                editMode={editMode}
            />

            <Dialog
                visible={locationPopupVisible}
                onHide={() => setLocationPopupVisible(false)}
                header="Customer Location"
                style={{ minWidth: '50vw' }}
            >
                {selectedCustomer && (
                    <GoogleMapComponent
                        lat={selectedCustomer.latitude || defaultLocation.latitude}
                        lng={selectedCustomer.longitude || defaultLocation.longitude}
                        radius={defaultLocation.radius}
                        editable={false}
                    />
                )}
            </Dialog>
        </div>
    );
};

export default Customers;
