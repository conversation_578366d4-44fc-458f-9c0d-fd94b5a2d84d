import { useState } from 'react';
import { Card } from 'primereact/card';
import { But<PERSON> } from 'primereact/button';
import { Accordion, AccordionTab } from 'primereact/accordion';
import { Dialog } from 'primereact/dialog';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { Dropdown } from 'primereact/dropdown';
import { Badge } from 'primereact/badge';
import {
    Phone,
    Mail,
    MessageCircle,
    HelpCircle,
    FileText,
    Users,
    Settings
} from 'lucide-react';

const Help = () => {
    const [contactDialogVisible, setContactDialogVisible] = useState(false);
    const [contactForm, setContactForm] = useState({
        subject: '',
        priority: 'medium',
        message: ''
    });

    const priorityOptions = [
        { label: 'High - Urgent', value: 'high' },
        { label: 'Medium - Normal', value: 'medium' },
        { label: 'Low - General Query', value: 'low' }
    ];

    const contactMethods = [
        {
            icon: Phone,
            title: 'Phone Support',
            description: 'Call our support team',
            contact: '+91-XXXX-XXXX-XX',
            availability: '9 AM - 6 PM (Mon-Sat)',
            color: 'text-green-400',
            bgColor: 'bg-green-500/10'
        },
        {
            icon: Mail,
            title: 'Email Support',
            description: 'Send us an email',
            contact: '<EMAIL>',
            availability: 'Response within 24 hours',
            color: 'text-blue-400',
            bgColor: 'bg-blue-500/10'
        },
        {
            icon: MessageCircle,
            title: 'Live Chat',
            description: 'Chat with admin team',
            contact: 'Available in app',
            availability: '9 AM - 6 PM (Mon-Sat)',
            color: 'text-purple-400',
            bgColor: 'bg-purple-500/10'
        }
    ];

    const handleContactSubmit = () => {
        // Here you would typically send the contact form to your backend
        console.log('Contact form submitted:', contactForm);
        setContactDialogVisible(false);
        setContactForm({ subject: '', priority: 'medium', message: '' });
    };

    return (
        <div className="min-h-screen bg-gray-900 p-6">
            {/* Header */}
            <div className="max-w-6xl mx-auto mb-8">
                <div className="text-center mb-8">
                    <h1 className="text-3xl font-bold text-white mb-4">
                        Provider Help Center
                    </h1>
                    <p className="text-gray-300 text-lg">
                        Get help with your provider account and connect with our admin team
                    </p>
                </div>

                {/* Quick Actions */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    {contactMethods.map((method, index) => {
                        const Icon = method.icon;
                        return (
                            <Card key={index} className="bg-gray-800 border border-gray-700">
                                <div className="text-center p-4">
                                    <div className={`${method.bgColor} ${method.color} p-3 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4`}>
                                        <Icon className="w-8 h-8" />
                                    </div>
                                    <h3 className="text-white font-semibold mb-2">{method.title}</h3>
                                    <p className="text-gray-400 text-sm mb-2">{method.description}</p>
                                    <p className="text-white font-medium mb-1">{method.contact}</p>
                                    <p className="text-gray-500 text-xs">{method.availability}</p>
                                </div>
                            </Card>
                        );
                    })}
                </div>

                {/* Contact Admin Button */}
                <div className="text-center mb-8">
                    <Button
                        label="Contact Admin Team"
                        icon="pi pi-send"
                        className="p-button-lg bg-purple-600 hover:bg-purple-700 border-purple-600"
                        onClick={() => setContactDialogVisible(true)}
                    />
                </div>
            </div>

            {/* FAQ Section */}
            <div className="max-w-4xl mx-auto">
                <h2 className="text-2xl font-bold text-white mb-6 text-center">
                    Frequently Asked Questions
                </h2>
                
                <Accordion multiple className="bg-gray-800">
                    <AccordionTab 
                        header={
                            <div className="flex items-center gap-3">
                                <CheckCircle className="w-5 h-5 text-green-400" />
                                <span className="text-white">How do I get my provider account approved?</span>
                            </div>
                        }
                    >
                        <div className="text-gray-300 space-y-3">
                            <p>To get your provider account approved:</p>
                            <ol className="list-decimal list-inside space-y-2 ml-4">
                                <li>Complete your profile with all required information</li>
                                <li>Upload necessary documents (business license, ID proof)</li>
                                <li>Verify your phone number and email address</li>
                                <li>Wait for admin review (usually 24-48 hours)</li>
                                <li>You'll receive an email notification once approved</li>
                            </ol>
                            <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 mt-4">
                                <p className="text-blue-300">
                                    <strong>Status Check:</strong> You can check your approval status in your Profile section.
                                </p>
                            </div>
                        </div>
                    </AccordionTab>

                    <AccordionTab 
                        header={
                            <div className="flex items-center gap-3">
                                <Users className="w-5 h-5 text-blue-400" />
                                <span className="text-white">How do I contact admin for urgent issues?</span>
                            </div>
                        }
                    >
                        <div className="text-gray-300 space-y-3">
                            <p>For urgent issues, you can contact admin through:</p>
                            <ul className="list-disc list-inside space-y-2 ml-4">
                                <li><strong>Phone:</strong> +91-XXXX-XXXX-XX (9 AM - 6 PM)</li>
                                <li><strong>Email:</strong> <EMAIL> (mark as urgent)</li>
                                <li><strong>Contact Form:</strong> Use the "Contact Admin Team" button above</li>
                            </ul>
                            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mt-4">
                                <p className="text-red-300">
                                    <strong>Emergency:</strong> For account suspension or payment issues, call directly.
                                </p>
                            </div>
                        </div>
                    </AccordionTab>

                    <AccordionTab 
                        header={
                            <div className="flex items-center gap-3">
                                <Settings className="w-5 h-5 text-purple-400" />
                                <span className="text-white">How do I update my business information?</span>
                            </div>
                        }
                    >
                        <div className="text-gray-300 space-y-3">
                            <p>To update your business information:</p>
                            <ol className="list-decimal list-inside space-y-2 ml-4">
                                <li>Go to your Profile section</li>
                                <li>Click "Edit Profile" button</li>
                                <li>Update the required fields</li>
                                <li>Upload new documents if needed</li>
                                <li>Submit for admin review</li>
                            </ol>
                            <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4 mt-4">
                                <p className="text-yellow-300">
                                    <strong>Note:</strong> Major changes may require re-approval from admin.
                                </p>
                            </div>
                        </div>
                    </AccordionTab>

                    <AccordionTab 
                        header={
                            <div className="flex items-center gap-3">
                                <FileText className="w-5 h-5 text-orange-400" />
                                <span className="text-white">What documents do I need for verification?</span>
                            </div>
                        }
                    >
                        <div className="text-gray-300 space-y-3">
                            <p>Required documents for provider verification:</p>
                            <ul className="list-disc list-inside space-y-2 ml-4">
                                <li>Business License or Registration Certificate</li>
                                <li>Government-issued ID (Aadhar, PAN, Passport)</li>
                                <li>Address Proof (Utility bill, Bank statement)</li>
                                <li>Bank Account Details</li>
                                <li>GST Certificate (if applicable)</li>
                            </ul>
                            <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4 mt-4">
                                <p className="text-green-300">
                                    <strong>Tip:</strong> Ensure all documents are clear and readable for faster approval.
                                </p>
                            </div>
                        </div>
                    </AccordionTab>

                    <AccordionTab 
                        header={
                            <div className="flex items-center gap-3">
                                <AlertCircle className="w-5 h-5 text-red-400" />
                                <span className="text-white">My account is suspended. What should I do?</span>
                            </div>
                        }
                    >
                        <div className="text-gray-300 space-y-3">
                            <p>If your account is suspended:</p>
                            <ol className="list-decimal list-inside space-y-2 ml-4">
                                <li>Check your email for suspension reason</li>
                                <li>Contact admin immediately via phone or email</li>
                                <li>Provide any requested documentation</li>
                                <li>Follow admin instructions for reactivation</li>
                                <li>Wait for account review and reactivation</li>
                            </ol>
                            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4 mt-4">
                                <p className="text-red-300">
                                    <strong>Important:</strong> Do not create a new account. Contact admin to resolve the issue.
                                </p>
                            </div>
                        </div>
                    </AccordionTab>

                    <AccordionTab 
                        header={
                            <div className="flex items-center gap-3">
                                <HelpCircle className="w-5 h-5 text-cyan-400" />
                                <span className="text-white">How do I track my application status?</span>
                            </div>
                        }
                    >
                        <div className="text-gray-300 space-y-3">
                            <p>To track your application status:</p>
                            <ol className="list-decimal list-inside space-y-2 ml-4">
                                <li>Login to your provider account</li>
                                <li>Go to Profile section</li>
                                <li>Check the "Account Status" section</li>
                                <li>Status will show: Pending, Under Review, Approved, or Rejected</li>
                            </ol>
                            <div className="space-y-2 mt-4">
                                <div className="flex items-center gap-2">
                                    <Badge value="Pending" severity="warning" />
                                    <span className="text-sm">Application submitted, waiting for review</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Badge value="Under Review" severity="info" />
                                    <span className="text-sm">Admin is reviewing your application</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Badge value="Approved" severity="success" />
                                    <span className="text-sm">Account approved and active</span>
                                </div>
                                <div className="flex items-center gap-2">
                                    <Badge value="Rejected" severity="danger" />
                                    <span className="text-sm">Application rejected, contact admin</span>
                                </div>
                            </div>
                        </div>
                    </AccordionTab>
                </Accordion>
            </div>

            {/* Contact Dialog */}
            <Dialog
                header="Contact Admin Team"
                visible={contactDialogVisible}
                style={{ width: '500px' }}
                onHide={() => setContactDialogVisible(false)}
                className="bg-gray-800"
            >
                <div className="space-y-4">
                    <div>
                        <label className="block text-white mb-2">Subject</label>
                        <InputText
                            value={contactForm.subject}
                            onChange={(e) => setContactForm({...contactForm, subject: e.target.value})}
                            placeholder="Brief description of your issue"
                            className="w-full"
                        />
                    </div>
                    
                    <div>
                        <label className="block text-white mb-2">Priority</label>
                        <Dropdown
                            value={contactForm.priority}
                            options={priorityOptions}
                            onChange={(e) => setContactForm({...contactForm, priority: e.value})}
                            className="w-full"
                        />
                    </div>
                    
                    <div>
                        <label className="block text-white mb-2">Message</label>
                        <InputTextarea
                            value={contactForm.message}
                            onChange={(e) => setContactForm({...contactForm, message: e.target.value})}
                            placeholder="Describe your issue in detail..."
                            rows={5}
                            className="w-full"
                        />
                    </div>
                    
                    <div className="flex justify-end gap-2 pt-4">
                        <Button
                            label="Cancel"
                            className="p-button-secondary"
                            onClick={() => setContactDialogVisible(false)}
                        />
                        <Button
                            label="Send Message"
                            className="p-button-primary"
                            onClick={handleContactSubmit}
                        />
                    </div>
                </div>
            </Dialog>
        </div>
    );
};

export default Help;
