Stack trace:
Frame         Function      Args
0007FFFF9DD0  00021006118E (00021028DEE8, 000210272B3E, 0007FFFF9DD0, 0007FFFF8CD0) msys-2.0.dll+0x2118E
0007FFFF9DD0  0002100469BA (000000000000, 000000000000, 000000000000, 0007FFFFA0A8) msys-2.0.dll+0x69BA
0007FFFF9DD0  0002100469F2 (00021028DF99, 0007FFFF9C88, 0007FFFF9DD0, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9DD0  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9DD0  00021006A545 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0007FFFFA0B0  00021006B9A5 (0007FFFF9DE0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC79620000 ntdll.dll
7FFC77D90000 KERNEL32.DLL
7FFC76C50000 KERNELBASE.dll
7FFC792A0000 USER32.dll
7FFC76960000 win32u.dll
7FFC77610000 GDI32.dll
7FFC76990000 gdi32full.dll
7FFC77310000 msvcp_win.dll
7FFC77040000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFC786D0000 advapi32.dll
7FFC779D0000 msvcrt.dll
7FFC773E0000 sechost.dll
7FFC77BE0000 RPCRT4.dll
7FFC75D70000 CRYPTBASE.DLL
7FFC768C0000 bcryptPrimitives.dll
7FFC79260000 IMM32.DLL
