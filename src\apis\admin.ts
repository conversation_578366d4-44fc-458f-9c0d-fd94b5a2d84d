// import { ProviderFormData } from '../types/provider';
import { CategoryFormData, ProductFormData, QuestionFormData,DashboardCountResponse, ProviderDashboardCountResponse } from '../types/global';
import api from './index';

export const getProviders = async (params = {}): Promise<any>  => {
  const queryString = new URLSearchParams(params).toString();
  const response = await api.get(`-admin/provider?${queryString}`);
  return response.data;
};




/**
 * Get Deshboard Count.
 *
 * GET -admin/dashboard
 *
 * @returns {Promise<any>} The response data of the newly created provider.
 */
export const getDeshboardCount = async (): Promise<DashboardCountResponse>  => {
  const response = await api.get('-admin/dashboard');
  return response.data;
};

/**
 * Get Provider Deshboard Count.
 *
 * GET provider/dashboard
 *
 * @returns {Promise<any>} The response data of the newly created provider.
 */
export const getProviderDeshboardCount = async (): Promise<ProviderDashboardCountResponse>  => {
  const response = await api.get('provider/dashboard');
  return response.data;
};

/**
 * Create a new provider.
 *
 * POST -admin/provider
 *
 * @param {FormData} data - The provider data to be created.
 * @returns {Promise<any>} The response data of the newly created provider.
 */
export const createProvider = async (data: FormData): Promise<any>  => {
  const response = await api.post('-admin/provider', data);
  return response.data;
};
/**
* Update an existing provider.
*
* PATCH -admin/provider
*
* @param {FormData} data - The provider data to be updated.
* @returns {Promise<any>} The response data of the updated provider.
*/
export const updateProvider = async (data: FormData): Promise<any>  => {
  const response = await api.patch(`-admin/provider`, data);
  return response.data;
};
/**
* Delete a provider by ID.
*
* DELETE -admin/provider/{provider}
*
* @param {string} provider - The ID of the provider to be deleted.
* @returns {Promise<any>} The response data of the deleted provider.
*/
export const deleteProvider = async (provider: string): Promise<any>  => {
  const response = await api.delete(`-admin/provider/${provider}`);
  return response.data;
};
/**
* Update the status of a provider.
*
* PATCH -admin/provider/status
*
* @param {string} provider - The ID of the provider whose status needs to be updated.
* @param {string} isApproved - The new status of the provider (e.g., "approved" or "rejected").
* @returns {Promise<any>} The response data of the updated provider status.
*/
export const updateProviderStatus = async (provider: string, isApproved: string): Promise<any>  => {
  const response = await api.patch(`-admin/provider/status`, { provider, isApproved });
  return response.data;
};

/**
* Fetch categories with optional pagination, sorting and search.
*
* GET -admin/category?{queryString}
*/
export const getCategories = async (params = {}) => {
  const queryString = new URLSearchParams(params as Record<string, any>).toString();
  const response = await api.get(`-admin/category?${queryString}`);
  return response.data;
};

/**
 * Create a new category.
 *
 * POST -admin/category
 *
 * @param data - Category data as FormData or JSON object.
 */
export const createCategory = async (data: CategoryFormData) => {
  const response = await api.post(`-admin/category`, data);
  return response.data;
};

/**
 * Update an existing category.
 *
 * PUT -admin/category
 *
 * @param data - Category data as FormData or JSON object (should include the category identifier).
 */
export const updateCategory = async (data: CategoryFormData) => {
  const response = await api.put(`-admin/category`, data);
  return response.data;
};

/**
 * Delete (soft delete) a category.
 *
 * DELETE -admin/category
 *
 * Note: The category id is sent in the request body as { category: categoryId }.
 *
 * @param categoryId - The unique identifier of the category to delete.
 */
export const deleteCategory = async (categoryId: string) => {
  const response = await api.delete(`-admin/category`, { data: { category: categoryId } });
  return response.data;
};

/**
 * Toggle the status of a category.
 *
 * PATCH -admin/category
 *
 * The backend expects the payload { category: categoryId, status: newStatus }.
 *
 * @param categoryId - The unique identifier of the category.
 * @param newStatus - The new status (typically a boolean) to set.
 */
export const updateCategoryStatus = async (categoryId: string) => {
  const response = await api.patch(`-admin/category`, { category: categoryId});
  return response.data;
};

/**
 * Fetch questions with optional pagination, sorting and search.
 *
 * GET -admin/question?{queryString}
 *
 * @param params - An object containing pagination, sorting and search parameters.
 */
export const getQuestions = async (params = {}) => {
  const queryString = new URLSearchParams(params).toString();
  const response = await api.get(`-admin/question?${queryString}`);
  return response.data;
};

/**
 * Create a new question.
 *
 * POST -admin/question
 *
 * @param data - Question data as FormData or a JSON object.
 */
export const createQuestion = async (data: QuestionFormData) => {
  const response = await api.post(`-admin/question`, data);
  return response.data;
};

/**
 * Update an existing question.
 *
 * PUT -admin/question
 *
 * @param data - Question data as FormData or a JSON object (should include the question identifier).
 */
export const updateQuestion = async (data: QuestionFormData) => {
  const response = await api.put(`-admin/question`, data);
  return response.data;
};

/**
 * Soft delete a question.
 *
 * DELETE -admin/question
 *
 * Note: The question id is sent in the request body as { question: questionId }.
 *
 * @param questionId - The unique identifier of the question to delete.
 */
export const deleteQuestion = async (questionId: string) => {
  const response = await api.delete(`-admin/question`, { data: { question: questionId } });
  return response.data;
};

/** Fetches a list of customers with pagination.
 *
 * @param { PaginationParams } params - The parameters for pagination.
 * @returns { Promise<any>} - A promise that resolves to the list of customers.
 */
export const getCustomers = async (params = {}): Promise<any>  => {
  const queryString = new URLSearchParams(params).toString();
  const response = await api.get(`-admin/customer?${queryString}`);
  return response.data;
};

/**
 * Creates a new customer.
 *
 * @param {FormData} formData - The form data containing customer information.
 * @returns {Promise<any>} - A promise that resolves to the created customer data.
 */
export const  createCustomer = async (formData: FormData): Promise<any>  => {
  const response = await api.post('-admin/customer', formData);
  return response.data;
};

/**
 * Updates an existing customer.
 *
 * @param {FormData} formData - The form data containing updated customer information.
 * @returns {Promise<any>} - A promise that resolves to the updated customer data.
 */
export const updateCustomer = async (formData: FormData): Promise<any>  => {
  const response = await api.patch('-admin/customer', formData);
  return response.data;
};

/**
 * Deletes a customer by their ID.
 *
 * @param {string} customerId - The ID of the customer to be deleted.
 * @returns {Promise<any>} - A promise that resolves to the response data from the delete operation.
 */
export const deleteCustomer = async (customerId: string): Promise<any>  => {
  const response = await api.delete(`-admin/customer/${customerId}`);
  return response.data;
};


/**
 * Fetches a list of products with pagination.
 *
 * @param {object} params - The parameters for pagination and search.
 */
export const getProducts = async (params= {}) => {
  const queryString = new URLSearchParams(params).toString();
  const response = await api.get(`-admin/product?${queryString}`);
  return response.data;
};

/**
 * Creates a new product.
 *
 * @param {FormData} formData - The form data containing product information.
 * @returns {Promise<any>} - A promise that resolves to the created product data.
 */
export const createProduct = async (formData: FormData|ProductFormData): Promise<any>  => {
  const response = await api.post(`-admin/product`, formData);
  return response.data;
};

/**
 * Updates an existing product.
 *
 * @param {FormData} formData - The form data containing updated product information.
 * @returns {Promise<any>} - A promise that resolves to the updated product data.
 */
export const updateProduct = async (formData: FormData|ProductFormData): Promise<any>  => {
  const response = await api.put(`-admin/product`, formData);
  return response.data;
};

/**
 * Deletes a product by its ID.
 *
 * @param {string} product - The ID of the product to be deleted.
 * @returns {Promise<any>} A promise that resolves to the response data from the delete operation.
 */
export const deleteProduct = async (product: string): Promise<any> => {
  // Note: For DELETE requests with a request body, Axios expects the body in the `data` property.
  const response = await api.delete(`-admin/product`, { data: { product } });
  return response.data;
};

// ################################################ Orders API ################################################

/**
 * Fetches a list of orders with pagination.
 *
 * @param {object} params - The parameters for pagination and search.
 * @returns {Promise<any>} - A promise that resolves to the list of orders.
 */
export const getOrders = async (params = {}): Promise<any> => {
  const queryString = new URLSearchParams(params).toString();
  const response = await api.get(`-admin/orders?${queryString}`);
  return response.data;
};

/**
 * Updates an order status.
 *
 * @param {string} orderId - The ID of the order to update.
 * @param {string} status - The new status for the order.
 * @returns {Promise<any>} - A promise that resolves to the updated order data.
 */
export const updateOrderStatus = async (orderId: string, status: string): Promise<any> => {
  const response = await api.put(`-admin/order/status`, { orderId, status });
  return response.data;
};

// ################################################ Reviews API ################################################

/**
 * Fetches a list of reviews with pagination.
 *
 * @param {object} params - The parameters for pagination and search.
 * @returns {Promise<any>} - A promise that resolves to the list of reviews.
 */
export const getReviews = async (params = {}): Promise<any> => {
  const queryString = new URLSearchParams(params).toString();
  const response = await api.get(`-admin/reviews?${queryString}`);
  return response.data;
};

/**
 * Delete the review.
 *
 * @param {object} params - The parameters for pagination and search.
 * @returns {Promise<any>} - A promise that resolves to the list of reviews.
 */
export const deleteReview = async (reviewId: string): Promise<any> => {
  const response = await api.delete(`-admin/review`, {
    data: { reviewId },
  });
  return response.data;
};