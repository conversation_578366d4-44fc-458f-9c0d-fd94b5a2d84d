import { useRef, useState, useEffect } from 'react';
import { Link, Outlet, useNavigate, useLocation } from 'react-router-dom';
import { Menu } from 'primereact/menu';
import { useAuth } from '../utils/AuthContext';
import { showSuccess } from '../utils/helper';
import Logo from "../assets/logo.svg";
import 'primeicons/primeicons.css';

const Layout = () => {
    const { logout } = useAuth();
    const navigate = useNavigate();
    const location = useLocation();

    const menuLeft = useRef<Menu>(null);
    const [isSidebarOpen, setIsSidebarOpen] = useState(true);
    const [profileStatus, setProfileStatus] = useState<'pending' | 'approved' | 'rejected' | null>(null);

    // Function to get profile status
    const getProfileStatus = () => {
        try {
            const cachedProfile = localStorage.getItem('providerProfile');
            if (cachedProfile) {
                const profile = JSON.parse(cachedProfile);
                return profile.isApproved || 'pending';
            }
        } catch (error) {
            console.error('Error reading profile status:', error);
        }
        return 'pending';
    };

    // Update profile status on mount and when localStorage changes
    useEffect(() => {
        setProfileStatus(getProfileStatus());

        // Listen for storage changes to update status in real-time
        const handleStorageChange = () => {
            setProfileStatus(getProfileStatus());
        };

        window.addEventListener('storage', handleStorageChange);

        // Also check periodically for updates
        const interval = setInterval(() => {
            setProfileStatus(getProfileStatus());
        }, 5000);

        return () => {
            window.removeEventListener('storage', handleStorageChange);
            clearInterval(interval);
        };
    }, []);

    // Dynamic navigation items based on approval status
    const getNavItems = () => {
        if (profileStatus === 'approved') {
            // Show all navigation items for approved providers
            return [
                { name: 'Dashboard', icon: 'pi pi-home', href: '/dashboard' },
                { name: 'Products', icon: 'pi pi-box', href: '/products' },
                { name: 'Orders', icon: 'pi pi-shopping-cart', href: '/orders' },
                { name: 'Reviews', icon: 'pi pi-star', href: '/reviews' },
                { name: 'Profile', icon: 'pi pi-user', href: '/profile' },
                { name: 'Help', icon: 'pi pi-question-circle', href: '/help' },
            ];
        } else {
            // Only show basic items for non-approved providers
            return [
                { name: 'Dashboard', icon: 'pi pi-home', href: '/dashboard' },
                { name: 'Profile', icon: 'pi pi-user', href: '/profile' },
                { name: 'Help', icon: 'pi pi-question-circle', href: '/help' }
            ];
        }
    };

    const navItems = getNavItems();

    const dynamicMenuItems = [
        { label: 'Profile', icon: 'pi pi-user', command: () => handleProfile() },
        { label: 'Logout', icon: 'pi pi-sign-out', command: () => handleLogout() },
    ];



    const handleProfile = () => {
        navigate('/profile');
    };

    const handleLogout = () => {
        logout();
        showSuccess("Logged out successfully");
    };

    return (
        <div className="h-screen flex flex-col bg-gray-900">
            {/* Top Navbar */}
            <nav className="fixed top-0 left-0 right-0 h-16 bg-gray-800 shadow-md z-10 flex items-center px-4 text-white">
                <button
                    onClick={() => setIsSidebarOpen(!isSidebarOpen)}
                    className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
                >
                    <span className="text-sm p-2">
                        <i className="pi pi-align-left"></i>
                    </span>
                </button>
                <h4 className="ml-2 font-semibold">Beepr</h4>
                <div className="flex-1" />
                <div className="flex items-center">
                    <Menu
                        model={dynamicMenuItems}
                        popup
                        ref={menuLeft}
                        id="popup_menu_left"
                        className="bg-gray-800 border border-gray-700"
                    />
                    <div
                        className="h-10 w-10 rounded-full cursor-pointer overflow-hidden"
                        onClick={(event) => menuLeft.current?.toggle(event)}
                        aria-controls="popup_menu_left"
                        aria-haspopup
                    >
                        <img className="h-full w-full object-cover" src={Logo} alt="Profile" />
                    </div>
                </div>
            </nav>

            <div className="flex flex-1 pt-16">
                {/* Sidebar */}
                <aside
                    className={`bg-gray-800 shadow-lg transform transition-all duration-300 fixed left-0 bottom-0 top-16 z-20 overflow-y-auto ${
                        isSidebarOpen ? 'w-48 translate-x-0' : '-translate-x-full'
                    } md:translate-x-0`}
                >
                    <div className="p-4 space-y-2">
                        {navItems.map((item) => (
                            <Link
                                key={item.name}
                                to={item.href}
                                className={`flex items-center p-3 rounded-lg group transition-colors ${
                                    location.pathname === item.href
                                        ? 'bg-[#CE93D8] text-[#121212]'
                                        : 'text-gray-300 hover:bg-gray-700'
                                }`}
                            >
                                <i className={`${item.icon} h-5 ${isSidebarOpen ? 'mr-3' : 'mr-0'}`}></i>
                                <span
                                    className={`${isSidebarOpen ? 'opacity-100 inline' : 'opacity-0 hidden'} transition`}
                                >
                                    {item.name}
                                </span>
                            </Link>
                        ))}
                    </div>
                </aside>

                {/* Main Content */}
                <main
                    className={`flex-1 transition-all duration-300 ${
                        isSidebarOpen ? 'md:ml-48' : 'md:ml-28'
                    } p-4 bg-gray-900`}
                >
                    <Outlet />
                </main>
            </div>
        </div>
    );
};

export default Layout;
